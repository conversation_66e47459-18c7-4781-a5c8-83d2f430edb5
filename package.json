{"name": "scb-partner-v1-portal-servicerequests", "version": "1.1.19", "description": "Service Request Microservice", "scripts": {"start": "node index.js", "test": "./node_modules/.bin/jest --updateSnapshot --coverage --verbose"}, "jest": {"testEnvironment": "node", "moduleNameMapper": {"^@domain(.*)$": "<rootDir>/src/domain$1"}, "coveragePathIgnorePatterns": ["/node_modules/"]}, "keywords": [], "author": "PartnerEco", "license": "ISC", "main": "index.js", "_moduleAliases": {"@root": ".", "@core": "src/core", "@src": "src", "@app": "src/app", "@domain": "src/domain", "@interface": "src/interface", "@infrastructure": "src/infrastructure"}, "dependencies": {"awilix": "^5.0.1", "aws-sdk": "^2.608.0", "axios": "^0.19.2", "deepmerge": "^4.2.2", "dotenv": "^8.2.0", "glob": "^7.1.6", "joi": "^14.3.1", "microservice-config": "git+ssh://***********************/Partner-Ecosystem/microservice-config-eks.git", "module-alias": "^2.2.2", "moment": "^2.24.0", "mysql2": "^2.1.0", "ramda": "^0.26.1", "scb-partner-commons": "git+ssh://***********************/Partner-Ecosystem/scb-partner-commons-eks.git", "sequelize": "^5.21.3", "tcomb": "^3.2.29", "uuid": "^3.4.0", "vault-helper": "git+ssh://***********************/Partner-Ecosystem/vault-helper.git"}, "devDependencies": {"chai": "^4.2.0", "jest": "^25.1.0"}}