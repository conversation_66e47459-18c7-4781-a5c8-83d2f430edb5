@startuml
title POST /v1/portal/servicerequests/leadform - Sequence Diagram

actor Client
participant "Express Server" as Server
participant "Router" as Router
participant "LeadForm Controller" as Controller
participant "CreateLeadform UseCase" as UseCase
participant "LeadForm Validator" as Validator
participant "LeadForm Service" as Service
participant "Reference Data Validation" as RefDataService
participant "LeadForm Repository" as Repository
participant "LeadForm Model" as Model
participant "MySQL Database" as Database
participant "Notification Service" as NotificationService
participant "Email Service" as EmailService
participant "External Email API" as EmailAPI

== Request Processing ==
Client -> Server: POST /v1/portal/servicerequests/leadform\n{leadform data}\nHeaders: {useruuid: "user-uuid"}

Server -> Router: Route request to controller
Router -> Controller: Execute handler(req, res, next)

== Header Validation ==
Controller -> Controller: Check req.headers['useruuid']
alt Missing useruuid header
    Controller -> Client: Error Response\n"No user unique identifier parsed with the request"
end

== Use Case Execution ==
Controller -> UseCase: create(userUuid, req.body)

== Input Validation ==
UseCase -> Validator: validate(leadformDTO)
Validator -> Validator: Joi schema validation\n- ownerType: ['company', 'individual']\n- taxId: 13 chars\n- contactEmail: valid email\n- etc.

alt Validation Error
    Validator -> UseCase: {error: validation details}
    UseCase -> Controller: throw {type: 'invalid_param'}
    Controller -> Client: Error Response
end

== Reference Data Validation ==
UseCase -> Service: validateLeadformReferenceData.getLeadformReferenceData(validatedData)
Service -> RefDataService: getLeadformReferenceData(leadform)

RefDataService -> Repository: leadServiceRepository.retrieveFromList(leadform.leadServices)
Repository -> Database: SELECT * FROM lead_service WHERE uuid IN (...)
Database -> Repository: Lead services data
Repository -> RefDataService: Lead services array

RefDataService -> Repository: leadChannelRepository.retrieveFromList(leadform.leadChannels)
Repository -> Database: SELECT * FROM lead_channel WHERE uuid IN (...)
Database -> Repository: Lead channels data
Repository -> RefDataService: Lead channels array

alt ownerType === 'company'
    RefDataService -> Repository: businessCategoryRepository.retrieveOneByUuid(businessCategoryUuid)
    Repository -> Database: SELECT * FROM business_category WHERE uuid = ?
    Database -> Repository: Business category data
    
    RefDataService -> Repository: businessTypeRepository.retrieveOneByUuid(businessTypeUuid)
    Repository -> Database: SELECT * FROM business_type WHERE uuid = ?
    Database -> Repository: Business type data
    
    RefDataService -> Repository: saleRangeRepository.retrieveOneByUuid(saleRangeUuid)
    Repository -> Database: SELECT * FROM sale_range WHERE uuid = ?
    Database -> Repository: Sale range data
    
    RefDataService -> Repository: transactionSizeRepository.retrieveOneByUuid(transactionSizeUuid)
    Repository -> Database: SELECT * FROM transaction_size WHERE uuid = ?
    Database -> Repository: Transaction size data
end

RefDataService -> RefDataService: validateLeadValue(leadServices, leadChannels)

alt Reference Data Validation Error
    RefDataService -> Service: {error: "Unmatched lead services/channels"}
    Service -> UseCase: {error: validation error}
    UseCase -> Controller: throw {type: 'invalid_param'}
    Controller -> Client: Error Response
end

== Entity Creation & Persistence ==
UseCase -> UseCase: Create leadform entity from validated data
UseCase -> Repository: create(leadformEntity)

Repository -> Model: leadformModel.build(leadform)
Model -> Model: Generate requestNo, timestamps
Repository -> Model: addLeadServices(leadform.leadServices)
Repository -> Model: addLeadChannels(leadform.leadChannels)
Repository -> Model: save()

Model -> Database: BEGIN TRANSACTION
Model -> Database: INSERT INTO leadform (leadFormUuid, requestNo, ownerType, ...)
Database -> Model: Leadform record created

Model -> Database: INSERT INTO lead_form_service (leadFormUuid, leadServiceUuid, ...)
Database -> Model: Lead services associations created

Model -> Database: INSERT INTO lead_form_channel (leadFormUuid, leadChannelUuid, ...)
Database -> Model: Lead channels associations created

Model -> Database: COMMIT TRANSACTION
Database -> Repository: Saved leadform with associations
Repository -> UseCase: leadformEntity with UUID and timestamps

== Notification Processing ==
UseCase -> UseCase: Transform entity to notificationDTO\n{userUuid, leadFormUuid, ownerType, contactName, etc.}

UseCase -> Service: notifyLeadformCreation.notify(notificationDTO)
Service -> NotificationService: notify(leadformNotificationDTO)

NotificationService -> Database: SELECT * FROM userinfo WHERE portalUuid = ?
Database -> NotificationService: User information

alt User Not Found
    NotificationService -> Service: throw {type: 'not_found'}
    Service -> UseCase: Error processing notification
    UseCase -> Controller: throw errorHandler.lookupType(error.type)
    Controller -> Client: Error Response
end

NotificationService -> NotificationService: Prepare email templates\n- Internal email payload\n- External email payload

alt config.email.deliver === true
    NotificationService -> EmailService: send(internalPayload, {language})
    EmailService -> EmailAPI: POST /v1/portal/email\nHeaders: {accept-language, host, requestUId, resourceOwnerId}
    EmailAPI -> EmailService: Email sent confirmation
    
    NotificationService -> EmailService: send(externalPayload, {language})
    EmailService -> EmailAPI: POST /v1/portal/email\nHeaders: {accept-language, host, requestUId, resourceOwnerId}
    EmailAPI -> EmailService: Email sent confirmation
end

NotificationService -> Service: Notification completed
Service -> UseCase: Notification success

== Response Preparation ==
UseCase -> UseCase: Prepare response DTO\n{leadformUuid, created_at}
UseCase -> Controller: Return success DTO

Controller -> Controller: response.success('success', createLeadformDTO)
Controller -> Client: HTTP 200 OK\n{\n  "status": "success",\n  "data": {\n    "leadformUuid": "uuid",\n    "created_at": "timestamp"\n  }\n}

== Error Handling ==
note over Controller, Client
  Any errors in the flow are caught by the controller
  and returned as structured error responses using
  the errorHandler.lookupType() method
end note

@enduml
