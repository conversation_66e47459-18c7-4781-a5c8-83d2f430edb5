# Service Requests Microservice
This microservice handles leadform creation and data preparation for the lead form

## New API structure introduction
Please note that this microservice implements the new structure on top of the existing common libraries (scb-partner-commons) which is currently implemented in interface and infrastructure layer

## Current Structure
This microservice embraces Domain Driven Design (DDD) introduced by <PERSON> back in 2003 and Onion Architecture. Thus, the application is splitted into different layers as follows

1. Application Layer
2. Domain Layer
3. Infrastructure Layer
4. Interface / Presentation Layer

![](https://miro.medium.com/max/6640/1*YD1UlRMg0Rbp8kUKDy1KZw.png)

### Application Layer
The application layer defines the operations that the application needs to perform to solve the business problems. It directs the expressive domain objects and makes use of required services from domain or infrastructure layer to drive the workflow of the application

### Domain Layer
This layer represents the business concepts and rules. There is no direct dependency on any other layer. Thus, it is persistence-ignorant and also infrastructure-ignorant

### Infrastructure Layer
The infrastructure layer is where the implementation of persistence logic; normally through repositories. The repositories, would normally implements the contracts / interfaces defined in the domain layer. But as the dynamic typed system is used (<PERSON>illa JS), there is no contract to work with.

The infrastructure layer also implements other types of services. For instance, logging or caching service.

### Interface Layer
It is the layer where the client-facing part of the application e.g. Web API is implemented. In this case, scb-partner-commons which is devloped on top of Restify is used







