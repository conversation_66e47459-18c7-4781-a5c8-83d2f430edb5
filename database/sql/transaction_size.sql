# ************************************************************
# Sequel Pro SQL dump
# Version 5446
#
# https://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 8.0.18)
# Database: customportal_dev
# Generation Time: 2020-01-24 12:05:35 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table transaction_size
# ------------------------------------------------------------

DROP TABLE IF EXISTS `transaction_size`;

CREATE TABLE `transaction_size` (
  `transactionSizeUuid` varchar(36) NOT NULL,
  `value` varchar(60) DEFAULT NULL,
  `created_at` bigint(20) NOT NULL,
  `updated_at` bigint(20) NOT NULL,
  PRIMARY KEY (`transactionSizeUuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `transaction_size` WRITE;
/*!40000 ALTER TABLE `transaction_size` DISABLE KEYS */;

INSERT INTO `transaction_size` (`transactionSizeUuid`, `value`, `created_at`, `updated_at`)
VALUES
	('0ed4dbe7-e470-4565-aed8-35ae3fb3e9d3','10 - 99',1579576099,1579576099),
	('1b2dc79a-b699-4d90-8c90-325925018d9d','100 - 999',1579576099,1579576099),
	('2f84a2c3-c42f-4b85-8253-4e1f43aa4b3e','1,000 - 4,999',1579576099,1579576099),
	('3bc8e64b-3b18-4a92-a799-98f0dd7302b6','5,000 - 9,999',1579576099,1579576099),
	('5d30556f-2cd4-481e-811f-a424647363b8','10,000 - 49,999',1579576099,1579576099),
	('6fa3c6e2-7cb3-403b-a047-e249d7373a0d','50,000 - 99,999',1579576099,1579576099),
	('7aa729e8-9936-4c43-8bdc-f8b46a52ac30','100,000 Up',1579576099,1579576099);

/*!40000 ALTER TABLE `transaction_size` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
