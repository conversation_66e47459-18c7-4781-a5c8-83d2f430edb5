# ************************************************************
# Sequel Pro SQL dump
# Version 5446
#
# https://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 8.0.18)
# Database: customportal_dev
# Generation Time: 2020-01-24 12:05:21 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table sale_range
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sale_range`;

CREATE TABLE `sale_range` (
  `saleRangeUuid` varchar(36) NOT NULL,
  `value` varchar(60) DEFAULT NULL,
  `created_at` bigint(20) NOT NULL,
  `updated_at` bigint(20) NOT NULL,
  PRIMARY KEY (`saleRangeUuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `sale_range` WRITE;
/*!40000 ALTER TABLE `sale_range` DISABLE KEYS */;

INSERT INTO `sale_range` (`saleRangeUuid`, `value`, `created_at`, `updated_at`)
VALUES
	('019c2361-613a-4564-84fd-08a2e263dace','10,000 - 99,999',1579576099,1579576099),
	('2e5faf13-4844-4678-b625-2911c8096d2b','100,000 - 499,999',1579576099,1579576099),
	('3633ac9d-ad85-469f-8486-9620301e2b62','500,000 - 999,999',1579576099,1579576099),
	('67ca6093-b6da-4bf0-ad44-46112eb735c4','1,000,000 - 4,999,999',1579576099,1579576099),
	('952c2347-0d6f-4074-aad9-ffc3866df0b5','5,000,000 - 9,999,999',1579576099,1579576099),
	('a8aa9037-545b-4cc1-bf74-a947773632f0','10,000,000 - 49,999,999',1579576099,1579576099),
	('aeba483d-0909-422e-993c-154643e37e98','50,000,000 - 99,999,999',1579576099,1579576099),
	('ba59d775-bc07-4634-985a-cff55b5c6a10','50,000,000 - 99,999,999',1579576099,1579576099),
	('bb7b65b1-dc2f-4222-b68e-0fb48af4f493','100,000,000 Up',1579576099,1579576099);

/*!40000 ALTER TABLE `sale_range` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
