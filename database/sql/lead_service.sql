# ************************************************************
# Sequel Pro SQL dump
# Version 5446
#
# https://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 8.0.18)
# Database: customportal_dev
# Generation Time: 2020-01-22 14:35:57 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table lead_service
# ------------------------------------------------------------

DROP TABLE IF EXISTS `lead_service`;

CREATE TABLE `lead_service` (
  `leadServiceUuid` varchar(36) NOT NULL,
  `nameEn` varchar(255) DEFAULT NULL,
  `nameTh` varchar(255) DEFAULT NULL,
  `s3_bucketName` varchar(128) DEFAULT NULL,
  `s3_bucketKey` varchar(255) DEFAULT NULL,
  `created_at` bigint(20) NOT NULL,
  `updated_at` bigint(20) NOT NULL,
  PRIMARY KEY (`leadServiceUuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `lead_service` WRITE;
/*!40000 ALTER TABLE `lead_service` DISABLE KEYS */;

INSERT INTO `lead_service` (`leadServiceUuid`, `nameEn`, `nameTh`, `s3_bucketName`, `s3_bucketKey`, `created_at`, `updated_at`)
VALUES
	('4d9a46bc-60ef-4be4-b1d2-763a5cc92860', 'Thai QR Code Payment', 'Thai QR Code Payment', 'partner-eco-pci-dev', 'leadform/images/promptpay.png', 1579681158, 1579681158),
	('6153ca11-2038-46ae-a369-8a56e71f5f73', 'QR Credit Card Payment', 'QR Credit Card Payment', 'partner-eco-pci-dev', 'leadform/images/visa.png', 1579681158, 1579681158),
	('e254364b-6ba1-431e-9fe3-75ea57c590f8', ' My prompt QR (B Scan C)', ' My prompt QR (B Scan C)', 'partner-eco-pci-dev', 'leadform/images/myprompt.png', 1579681158, 1579681158),
	('e7b2a2f8-2bd9-4fbd-85ba-d14b53b5ccc8', 'QR WeChat , Alipay', 'QR WeChat , Alipay', 'partner-eco-pci-dev', 'leadform/images/alipay-wechat.png', 1579681158, 1579681158),
	('fb5d48ce-dacc-48f9-a01a-510ba6b420d6', 'SCB PayWise (Pay with SCB EASY)', 'SCB PayWise (Pay with SCB EASY)', 'partner-eco-pci-dev', 'leadform/images/scb.png', 1579681158, 1579681158);

/*!40000 ALTER TABLE `lead_service` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
