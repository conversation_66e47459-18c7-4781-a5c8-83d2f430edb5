# ************************************************************
# Sequel Pro SQL dump
# Version 5446
#
# https://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 8.0.18)
# Database: customportal_dev
# Generation Time: 2020-01-22 14:37:35 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table leadform
# ------------------------------------------------------------

DROP TABLE IF EXISTS `leadform`;

CREATE TABLE `leadform` (
  `leadFormUuid` varchar(36) NOT NULL,
  `requestNo` varchar(14) NOT NULL,
  `ownerType` enum('company','individual') NOT NULL,
  `taxId` varchar(13) NOT NULL,
  `ownerName` varchar(255) NOT NULL,
  `contactName` varchar(255) NOT NULL,
  `contactEmail` varchar(255) NOT NULL,
  `contactMobile` varchar(10) NOT NULL,
  `merchantNameEn` varchar(255) DEFAULT NULL,
  `merchantNameTh` varchar(255) DEFAULT NULL,
  `businessCategoryUuid` varchar(36) DEFAULT NULL,
  `saleRangeUuid` varchar(36) DEFAULT NULL,
  `transactionSizeUuid` varchar(36) DEFAULT NULL,
  `proportionForeignPay` decimal(10,0) DEFAULT NULL,
  `otherApplicationChannel` varchar(50) DEFAULT NULL,
  `created_at` bigint(20) DEFAULT NULL,
  `updated_at` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`leadFormUuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;




/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
