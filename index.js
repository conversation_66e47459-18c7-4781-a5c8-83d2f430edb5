require('module-alias/register');
require('dotenv').config();
const { enableVaultConfig } = require('vault-helper');

const startServer = () => {
const container = require('@src/container');

const app = container.resolve('app');

app.start();

}

const main = async () => {
    try {
      const listENV = ['dev', 'sit', 'uat', 'pt', 'prod'];
      if (listENV.includes(process.env.NODE_ENV)) {
        await enableVaultConfig('AP1970-PartnerEcosystem', ['DB_USER_PORTAL', 'DB_PASSWORD_PORTAL', 'CA_PORTAL_KEY', 'CA_PORTAL_SECRET', 'CROSS_ENV_KEY', 'CROSS_ENV_SECRET'], true);
      }
      startServer();
    } catch (error) {
      console.log('ERROR WHEN TRY TO START SERVER OR ENABLE VAULT CONFIG: ', error.response.data);
    }
};
  
  main();