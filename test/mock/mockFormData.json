{"status": {"code": 1000, "description": "Success"}, "data": {"businessTypes": [{"businessTypeUuid": "f9aae1e8-4b5a-4713-8abd-cd4285afbf1f", "nameTh": "ร้านค้าทั่วไป ", "nameEn": "Retail Store", "businessCategories": [{"businessCategoryUuid": "03a3dfd1-b441-410d-86b1-eadf1da4fe42", "nameTh": "เครื่องสำอางค์", "nameEn": "Cosmetic Stores"}, {"businessCategoryUuid": "12682844-8175-4d84-847d-f1864fa7479c", "nameTh": "ซัก - อบ - รีด -  ร้านทั่วไป", "nameEn": "Laundry Services"}, {"businessCategoryUuid": "23a96cdf-b182-4948-92ac-658c1e774e30", "nameTh": "ร้านขายทอง ", "nameEn": "Gold Shop"}, {"businessCategoryUuid": "2f1a1818-7353-4f51-985e-7a25b03895aa", "nameTh": "บริษัท รับจ้างทำความสะอาด", "nameEn": "Cleaning  Services"}, {"businessCategoryUuid": "3825109b-15bf-4c29-a7b5-3510dca26b0a", "nameTh": "จำหน่ายอุปกรณ์กีฬาอุปกรณ์", "nameEn": "Sporting Stores"}, {"businessCategoryUuid": "67f71dd4-1968-4744-85f5-d366074e3c16", "nameTh": "คอมพิวเตอร์ - ขายและซ่อมเครื่องและอุปกรณ์", "nameEn": "Electronic Shop"}, {"businessCategoryUuid": "75129bdd-c46e-4dd3-bd03-9730d2616dcb", "nameTh": "ร้านขายแว่นตา", "nameEn": "Optical Shops"}, {"businessCategoryUuid": "883dec9b-c292-45e6-8aa3-ed4567ec0a99", "nameTh": "ดอกไม้", "nameEn": "Florists"}, {"businessCategoryUuid": "8c4cf195-e87a-47fc-88f2-f143431d2f69", "nameTh": "สินค้าเบ็ดเตล็ด", "nameEn": "Retail Stores"}, {"businessCategoryUuid": "9780a039-947d-4226-9e1f-973d2352baa2", "nameTh": "ร้านขายหนังสือ ", "nameEn": "Book Stores"}, {"businessCategoryUuid": "9ae14cd7-af6d-4a7c-9c5c-abceff18ac5a", "nameTh": "จำหน่ายเครื่องดนตรี", "nameEn": "Music Shop"}, {"businessCategoryUuid": "acb64060-a7d7-4e90-b670-2e2f0ae72d08", "nameTh": "ของเด็กเล่น ", "nameEn": "Toy Shops"}, {"businessCategoryUuid": "bed9c98f-db0c-4a14-9978-6e3afd21c901", "nameTh": "สัตว์เลี้ยง", "nameEn": "Pet Shops"}]}, {"businessTypeUuid": "71ab177a-b925-45a2-bd66-cc8f938591d6", "nameTh": "บริการให้เช่า", "nameEn": "Rent Service ", "businessCategories": [{"businessCategoryUuid": "08c394e0-709a-470c-ad8f-5ab9073a6072", "nameTh": "ให้เช่าเรือ  ", "nameEn": "Boat Rental "}, {"businessCategoryUuid": "a3cee424-**************-4f0d837d2f9d", "nameTh": "ให้เช่ารถ", "nameEn": "Car Rental"}]}, {"businessTypeUuid": "4dee78cc-7045-4f0a-8d11-68eda052710f", "nameTh": "การศึกษา", "nameEn": "Education ", "businessCategories": [{"businessCategoryUuid": "0dc4db49-998d-4702-8add-15935bd97bd2", "nameTh": "โรงเรียนกวดวิชา, สอนภาษา", "nameEn": "Schools and Educational Services"}, {"businessCategoryUuid": "c9ffb619-287a-401f-972a-23671caa801f", "nameTh": "มหาวิทยาลัย", "nameEn": "Colleges, Universities"}]}, {"businessTypeUuid": "945db54a-3f28-4150-8fb1-fe221ea48e66", "nameTh": "การสื่อสาร", "nameEn": "Telecomm", "businessCategories": [{"businessCategoryUuid": "1212b344-18ac-466b-a3eb-0991a0aae644", "nameTh": "จำหน่ายโทรศัพท์", "nameEn": "Mobile Phone Shop"}, {"businessCategoryUuid": "14ce2403-7177-416f-b6e5-3f18cd23213f", "nameTh": "ค่าใช้บริการโทรศัพท์", "nameEn": "Telecommunication Services"}]}, {"businessTypeUuid": "a80a97f8-805a-4c9f-8260-4b5c40803b8d", "nameTh": "ยานยนต์", "nameEn": "Automotive", "businessCategories": [{"businessCategoryUuid": "17820f2f-30c0-4149-9695-1440959c13d1", "nameTh": "อะไหล่ยนต์", "nameEn": "Parts Stores"}, {"businessCategoryUuid": "297359b3-3570-48a0-98a9-154a211ec37c", "nameTh": "ล้าง-ขัด-เคลือบสี รถยนต์", "nameEn": "Car Washes"}, {"businessCategoryUuid": "77f2ee17-eea2-40f3-9600-018b2a02516b", "nameTh": "อู่ซ่อมรถ ", "nameEn": "Automotive Service shops"}, {"businessCategoryUuid": "8cb44f22-684b-43f2-b0b5-9972d7055423", "nameTh": "จำหน่ายเรือ ", "nameEn": "Boat Dealers"}, {"businessCategoryUuid": "ab0cbf3b-b46e-4b76-bca7-8e9ff33a07d5", "nameTh": "จำหน่ายรถยนต์", "nameEn": "Car Dealers"}, {"businessCategoryUuid": "b2c08925-0fa7-435d-81c4-fa675f3febd6", "nameTh": "จำหน่ายยางรถยนต์", "nameEn": "Tire Stores"}, {"businessCategoryUuid": "c8d6959c-2f6d-4bb3-a1fa-d418252393b7", "nameTh": "ปั๊มน้ำมัน(อัตโนมัติ) ชนิดเติมเอง", "nameEn": "Automated Fuel Dispensers"}, {"businessCategoryUuid": "d09a4756-50c7-4ceb-867c-c694d8d88a2d", "nameTh": "ที่จอดรถ ", "nameEn": "Car Parking"}, {"businessCategoryUuid": "d24103be-b10e-455b-bbb8-dec50f1066e8", "nameTh": "ปั๊มน้ำมัน ", "nameEn": "Gas Stations"}, {"businessCategoryUuid": "ff9c5318-39f5-42e3-8bc0-bc751f677d8c", "nameTh": "จำหน่ายรถมอเตอร์ไซด์", "nameEn": "Motorcycle Shops"}]}, {"businessTypeUuid": "b29bd6d8-ffc9-40a2-ba62-dac6debef1d2", "nameTh": "บริการทั่วไป", "nameEn": "General Service", "businessCategories": [{"businessCategoryUuid": "1c17dc43-b3b1-46ce-9f9b-31dae683b811", "nameTh": "ร้านตัดผมชาย หรือ ร้านเสริมสวย", "nameEn": "Beauty and Barber Shops"}]}, {"businessTypeUuid": "51c96361-c08a-4cda-bdca-7c5e0b052dee", "nameTh": "ร้านอาหาร", "nameEn": "Restaurant", "businessCategories": [{"businessCategoryUuid": "3bc0580f-10c0-4b9d-a3dd-93a084d6a0d2", "nameTh": "ร้านอาหาร, ภัตตาคาร", "nameEn": "Restaurant"}]}, {"businessTypeUuid": "880318e2-cee1-47e9-802d-4abe1082d4fd", "nameTh": "การขนส่ง", "nameEn": "Transportation", "businessCategories": [{"businessCategoryUuid": "42aaf2ad-aa45-4a3d-bdeb-719d34045ba2", "nameTh": "แท็กซี่ - TAXI - ค่าโดยสาร", "nameEn": "Limousines and Taxicabs"}, {"businessCategoryUuid": "536ed495-81ce-497d-baeb-c6fdf64b822c", "nameTh": "เรือเดินทะเล, เรือนำเที่ยว ", "nameEn": "Steamship Lines"}, {"businessCategoryUuid": "767582d1-3beb-4a61-bac6-999cb02edc72", "nameTh": "รับส่งเอกสาร, พัสดุ  ", "nameEn": "Courier Services"}]}, {"businessTypeUuid": "3ab79567-4499-4d76-9aa0-f557ba85f926", "nameTh": "อาหาร", "nameEn": "Fast Food Restaurants", "businessCategories": [{"businessCategoryUuid": "4ac52df7-3d20-4e9d-9abf-42786fee8b85", "nameTh": "ฟาสต์ ฟู๊ด (ชำระเงินที่ counter) ", "nameEn": "Fast Food Restaurants"}, {"businessCategoryUuid": "f9fa77e2-6fee-4982-8d0e-733d282a2315", "nameTh": "ร้านกาแฟ", "nameEn": "Coffee Shops"}]}, {"businessTypeUuid": "00c87b1e-d53f-4b05-b15f-5dc6b7392894", "nameTh": "พิพิธภัณฑ์สัตว์น้ำ", "nameEn": "Aquariums", "businessCategories": [{"businessCategoryUuid": "515b913e-3c69-4356-a4d7-9dbb76db5496", "nameTh": "การแสดง - สัตว์น้ำ ", "nameEn": "Aquariums"}]}, {"businessTypeUuid": "ac713d26-c7a2-49ad-9b10-8f3a267de2f2", "nameTh": "สรรพสินค้า", "nameEn": "Department Stores", "businessCategories": [{"businessCategoryUuid": "59be17f6-a882-471a-9ab7-67a10fd277c9", "nameTh": "ร้านปลอดภาษี", "nameEn": "Duty-Free Stores"}, {"businessCategoryUuid": "d35cc96f-bf19-4332-9dce-0a1eca1b84e0", "nameTh": "ห้างสรรพสินค้า", "nameEn": "Department Stores"}]}, {"businessTypeUuid": "66fffc5f-2fd4-423b-a85f-87ace0ee6b94", "nameTh": "ร้านขายยา", "nameEn": "Drug Stores", "businessCategories": [{"businessCategoryUuid": "5def97cf-4d24-46a8-b196-ddeeccd8a067", "nameTh": "ร้านขายยา", "nameEn": "Drug Stores"}]}, {"businessTypeUuid": "2ef81f89-7600-47f9-8dd7-837de4248e88", "nameTh": "ประกันภัย", "nameEn": "Insurance ", "businessCategories": [{"businessCategoryUuid": "641a74ca-f4d4-427c-a717-dd7632380010", "nameTh": "ประกันภัย, ประกันชีวิต ", "nameEn": "Insurance"}]}, {"businessTypeUuid": "5c181bda-514e-4c96-87b2-2bf938784924", "nameTh": "รัฐบริการ", "nameEn": "Government Services", "businessCategories": [{"businessCategoryUuid": "67242d46-eb7f-4ee2-99ef-3c9989a53678", "nameTh": "ค่าภาษี", "nameEn": "Tax Payment"}]}, {"businessTypeUuid": "c805d3a0-0d20-4fef-84bb-8c017d71d16a", "nameTh": "การบิน", "nameEn": "Airlines", "businessCategories": [{"businessCategoryUuid": "680bd5b0-79a4-4d03-9f85-133b35119a4f", "nameTh": "สายการบิน", "nameEn": "Airlines and Air Carriers"}]}, {"businessTypeUuid": "9ae28567-137f-472c-9c58-bc8aab812dfc", "nameTh": "ผู้ผลิตและจัดจำหน่าย", "nameEn": "Manufacturers and distributors", "businessCategories": [{"businessCategoryUuid": "7821aa6d-59d7-4243-b36a-c1de969fb7c4", "nameTh": "ผู้ผลิตและจัดจำหน่าย", "nameEn": "Manufacturers and distributors"}]}, {"businessTypeUuid": "0d5c7dec-cbc8-4b07-b281-92dcd6095711", "nameTh": "ขายตรง", "nameEn": "Direct  Marketing ", "businessCategories": [{"businessCategoryUuid": "81be1f16-fe16-4081-b3f2-b321f2ab7602", "nameTh": "ธุรกิจขายตรง", "nameEn": "Direct Marketing"}]}, {"businessTypeUuid": "c686effb-98a4-4726-9932-e8504d6c6ef6", "nameTh": "สมาคม / ชมรม", "nameEn": "Membership clubs", "businessCategories": [{"businessCategoryUuid": "904740f8-e028-47c6-8836-de6434a617a8", "nameTh": "สมาคม / ชมรม ", "nameEn": "Associations"}]}, {"businessTypeUuid": "91198fb7-f3d3-48ce-8e59-a443f53fc1da", "nameTh": "โรงแรม / ที่พัก", "nameEn": "Hotels", "businessCategories": [{"businessCategoryUuid": "915fd145-24f6-4057-a0eb-833ca0910dec", "nameTh": "โรงแรม , รีสอร์ท และ ที่พักแรม ", "nameEn": "Hotels and Resorts"}]}, {"businessTypeUuid": "949c58b9-7adc-4e44-b3fb-f4966f2fc890", "nameTh": "บ้าน / ที่อยู่อาศัย", "nameEn": "Home Store", "businessCategories": [{"businessCategoryUuid": "a37366f4-d911-415c-913c-e7cd56bf0468", "nameTh": "เครื่องใช้ในบ้าน ", "nameEn": "Household Appliance Stores"}, {"businessCategoryUuid": "e3783a2b-8492-48a7-9f72-7332735930fe", "nameTh": "เฟอร์นิเจอร์  ", "nameEn": "Furniture Shop"}]}, {"businessTypeUuid": "c57cfd33-c4ac-447b-82a4-fdbc904b6b87", "nameTh": "ร้านค้าปลีก ", "nameEn": "Supermarkets", "businessCategories": [{"businessCategoryUuid": "adc3799c-4d7b-42ae-a5d1-ff718083be89", "nameTh": "ร้านชำ - สโตร์ ขนาดกลาง - เล็ก", "nameEn": "Supermarkets"}]}, {"businessTypeUuid": "0963f766-e95a-4d95-a4d7-048784eb5304", "nameTh": "รัฐบริการ", "nameEn": "Utilities", "businessCategories": [{"businessCategoryUuid": "b40cdc05-944d-4f75-9088-818a6c425ecc", "nameTh": "น้ำประปา / ไฟฟ้า - ค่าบริการ", "nameEn": "Utilities - Electrics, Water, and Sanitary"}]}, {"businessTypeUuid": "cbd9b98f-c538-4a1c-bbac-0725df8c9618", "nameTh": "เครื่องแต่งกาย / เสื้อผ้า ", "nameEn": "Clothing Stores", "businessCategories": [{"businessCategoryUuid": "bec12008-bece-40da-9c87-55f94540cc5d", "nameTh": "จำหน่ายเสื้อผ้า ", "nameEn": "Clothing Stores"}]}, {"businessTypeUuid": "2eeb6e7a-492b-4497-8ff1-511eb8dcb89b", "nameTh": "สุขภาพ", "nameEn": "Health", "businessCategories": [{"businessCategoryUuid": "d60f4a1f-1f5b-4243-bb44-cc7303cea9e4", "nameTh": "นวดแผนโบราณ, นวดฝ่าเท้า, สปา", "nameEn": "Health and Beauty Spas"}]}, {"businessTypeUuid": "ac17f5c9-346f-4e40-87fb-e5670bbc0cc2", "nameTh": "สนามกอล์ฟ", "nameEn": "Golf Courses", "businessCategories": [{"businessCategoryUuid": "dddad506-3a03-412c-876e-4b95c93de49d", "nameTh": "สนามกอล์ฟ - ค่าเช่า,ค่าชั่วโมง", "nameEn": "Golf Courses"}]}, {"businessTypeUuid": "91149eb5-ce87-4afe-bb19-5d5df651a811", "nameTh": "ธุรกิจท่องเที่ยว", "nameEn": "Travel  Agencies", "businessCategories": [{"businessCategoryUuid": "f647e4c7-f892-46c6-a1b3-244c932af357", "nameTh": "ท่องเที่ยว, จัดนำเที่ยว", "nameEn": "Travel  Agencies"}]}, {"businessTypeUuid": "c25c22bf-bf04-46c9-ba18-df94c238fee8", "nameTh": "การบริจาค", "nameEn": "Donation", "businessCategories": [{"businessCategoryUuid": "fe401dd1-0f9a-4704-a508-b92930bbb8dc", "nameTh": "การบริจาค", "nameEn": "Charitable"}]}], "leadServices": [{"leadServiceUuid": "4d9a46bc-60ef-4be4-b1d2-763a5cc92860", "nameEn": "Thai QR Code Payment", "nameTh": "Thai QR Code Payment", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/promptpay.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=c3323fe73aecf2b03134736a8d06f515d7c13c29bee96d6bc75a0264a01805b4&X-Amz-SignedHeaders=host"}, {"leadServiceUuid": "6153ca11-2038-46ae-a369-8a56e71f5f73", "nameEn": "QR Credit Card Payment", "nameTh": "QR Credit Card Payment", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/visa.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=c55797380f5be647089223a875e496e20631fe3697e1d7b028602317bf0ae04d&X-Amz-SignedHeaders=host"}, {"leadServiceUuid": "e254364b-6ba1-431e-9fe3-75ea57c590f8", "nameEn": " My prompt QR (B Scan C)", "nameTh": " My prompt QR (B Scan C)", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/myprompt.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=3393370d98ac9662f697115443a48abe1a787901174115f6544658a381c4a330&X-Amz-SignedHeaders=host"}, {"leadServiceUuid": "e7b2a2f8-2bd9-4fbd-85ba-d14b53b5ccc8", "nameEn": "QR WeChat , Alipay", "nameTh": "QR WeChat , Alipay", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/alipay-wechat.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=228df9c2c6c70382f81568150a53e5e28890b8dd9ec21fe32d2c5adbd955ee22&X-Amz-SignedHeaders=host"}, {"leadServiceUuid": "fb5d48ce-dacc-48f9-a01a-510ba6b420d6", "nameEn": "SCB PayWise (Pay with SCB EASY)", "nameTh": "SCB PayWise (Pay with SCB EASY)", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/scb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=c096b5ae68f5baa1985878f5506edda7e488c9d20a8b6ba1757b48c880aa2ba0&X-Amz-SignedHeaders=host"}], "leadChannels": [{"leadChannelUuid": "040db9f7-d484-41d3-889e-86181ffaf411", "nameEn": "Mobile Application", "nameTh": "Mobile Application"}, {"leadChannelUuid": "1b952502-6d95-47a1-bda4-56c889d51d1c", "nameEn": "Mobile Website", "nameTh": "Mobile Website"}, {"leadChannelUuid": "35d11652-37e2-40d3-b79e-f84dbde53c9f", "nameEn": "Desktop Website", "nameTh": "Desktop Website"}, {"leadChannelUuid": "89a2760e-4348-4212-bfff-05d206bd4046", "nameEn": "POS", "nameTh": "POS"}, {"leadChannelUuid": "fe797c9a-516d-45d9-a4e9-eee7f89b74e1", "nameEn": "Kiosk", "nameTh": "Kiosk"}], "saleRanges": [{"saleRangeUuid": "019c2361-613a-4564-84fd-08a2e263dace", "value": "10,000 - 99,999"}, {"saleRangeUuid": "2e5faf13-4844-4678-b625-2911c8096d2b", "value": "100,000 - 499,999"}, {"saleRangeUuid": "3633ac9d-ad85-469f-8486-9620301e2b62", "value": "500,000 - 999,999"}, {"saleRangeUuid": "67ca6093-b6da-4bf0-ad44-46112eb735c4", "value": "1,000,000 - 4,999,999"}, {"saleRangeUuid": "952c2347-0d6f-4074-aad9-ffc3866df0b5", "value": "5,000,000 - 9,999,999"}, {"saleRangeUuid": "a8aa9037-545b-4cc1-bf74-a947773632f0", "value": "10,000,000 - 49,999,999"}, {"saleRangeUuid": "aeba483d-0909-422e-993c-154643e37e98", "value": "50,000,000 - 99,999,999"}, {"saleRangeUuid": "ba59d775-bc07-4634-985a-cff55b5c6a10", "value": "50,000,000 - 99,999,999"}, {"saleRangeUuid": "bb7b65b1-dc2f-4222-b68e-0fb48af4f493", "value": "100,000,000 Up"}], "transactionSizes": [{"transactionSizeUuid": "0ed4dbe7-e470-4565-aed8-35ae3fb3e9d3", "value": "10 - 99"}, {"transactionSizeUuid": "1b2dc79a-b699-4d90-8c90-325925018d9d", "value": "100 - 999"}, {"transactionSizeUuid": "2f84a2c3-c42f-4b85-8253-4e1f43aa4b3e", "value": "1,000 - 4,999"}, {"transactionSizeUuid": "3bc8e64b-3b18-4a92-a799-98f0dd7302b6", "value": "5,000 - 9,999"}, {"transactionSizeUuid": "5d30556f-2cd4-481e-811f-a424647363b8", "value": "10,000 - 49,999"}, {"transactionSizeUuid": "6fa3c6e2-7cb3-403b-a047-e249d7373a0d", "value": "50,000 - 99,999"}, {"transactionSizeUuid": "7aa729e8-9936-4c43-8bdc-f8b46a52ac30", "value": "100,000 Up"}]}}