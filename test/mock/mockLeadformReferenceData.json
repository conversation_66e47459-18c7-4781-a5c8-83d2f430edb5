{"businessType": {"businessTypeUuid": "71ab177a-b925-45a2-bd66-cc8f938591d6", "nameTh": "บริการให้เช่า", "nameEn": "Rent Service"}, "businessCategory": {"businessCategoryUuid": "08c394e0-709a-470c-ad8f-5ab9073a6072", "nameTh": "ให้เช่าเรือ", "nameEn": "Boat Rental "}, "saleRange": {"saleRangeUuid": "ba59d775-bc07-4634-985a-cff55b5c6a10", "value": "50,000,000 - 99,999,999"}, "transactionSize": {"transactionSizeUuid": "7aa729e8-9936-4c43-8bdc-f8b46a52ac30", "value": "100,000 Up"}, "leadServices": [{"leadServiceUuid": "4d9a46bc-60ef-4be4-b1d2-763a5cc92860", "nameEn": "Thai QR Code Payment", "nameTh": "Thai QR Code Payment", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/promptpay.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=c3323fe73aecf2b03134736a8d06f515d7c13c29bee96d6bc75a0264a01805b4&X-Amz-SignedHeaders=host"}, {"leadServiceUuid": "e7b2a2f8-2bd9-4fbd-85ba-d14b53b5ccc8", "nameEn": "QR WeChat , Alipay", "nameTh": "QR WeChat , Alipay", "url": "https://partnereco-pci-dev.s3.ap-southeast-1.amazonaws.com/alipay-wechat.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA6KLWHNH3WQNZ6F5E%2F20200127%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20200127T031315Z&X-Amz-Expires=300&X-Amz-Signature=228df9c2c6c70382f81568150a53e5e28890b8dd9ec21fe32d2c5adbd955ee22&X-Amz-SignedHeaders=host"}], "leadChannels": [{"leadChannelUuid": "fe797c9a-516d-45d9-a4e9-eee7f89b74e1", "nameEn": "Kiosk", "nameTh": "Kiosk"}]}