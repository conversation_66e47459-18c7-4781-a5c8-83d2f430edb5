const errorHandler = require('../mock/errorHandler');
const initFormUseCase = require('../../src/app/formInit/getInitialFormData');
const sourceData = require('../mock/mockFormData.json');

describe('Lead Form Initialisation', () => {
  let useCase;
  describe('Success', () => {
    beforeEach(() => {
      const repositories = [];
      [
        'businessTypeRepository',
        'leadServiceRepository',
        'leadChannelRepository',
        'saleRangeRepository',
        'transactionSizeRepository'
      ].forEach(repoName => {
        const repository = require('../mock/repositories/' + repoName);
        repositories[repoName] = repository;
      });
      useCase = initFormUseCase({...repositories, errorHandler});
    });

    it('Successfully Initialised with all required information', async () => {
      const useCaseResult = await useCase.initForm();
      expect(useCaseResult).toEqual(sourceData.data);
    });
  });

  describe('Failed', () => {
    beforeEach(() => {
      const repositories = [];
      const mockedRepository = {
        retrieveAll: () => Promise.reject('Error at one of repositories')
      }
      
      repositories.businessTypesRepository = mockedRepository;
      repositories.leadServiceRepository = mockedRepository;
      repositories.leadChannelRepository = mockedRepository;
      repositories.saleRangeRepository = mockedRepository;
      repositories.transactionSizeRepository = mockedRepository;

      useCase = initFormUseCase({ ...repositories, errorHandler });

    });

    it('Display an error on rejection', async () => {
      let error;
      try {
        await useCase.initForm();
      } catch (err) {
        error = err.message
      } 

      expect(error).toEqual('Test Error');
    });
    
    
  });
});