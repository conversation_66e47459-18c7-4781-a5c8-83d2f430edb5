require('module-alias/register');
const leadformRepository = require('../mock/repositories/leadformRepository');
const errorHandler = require('../mock/errorHandler');
const createLeadformUseCase = require('../../src/app/leadform/create');
const mockedLeadformRefData = require('../mock/mockLeadformReferenceData.json');
const leadformValidator  = require('../../src/infrastructure/validators/leadform');

describe('Lead form creation', () => {
  // Global use case which can be overridden anytime from below blocks
  let useCase;

  describe('Success cases', () => {
    beforeEach(() => {
      const logger = {
        debug: message => {
          return true;
        },
        error: message => {
          return true;
        }
      }

      const leadformService = {
        validateLeadformReferenceData: {
          getLeadformReferenceData: value => ({ data: mockedLeadformRefData })
        },
        notifyLeadformCreation: {
          notify: dto => true
        }
      }

      const repositories = [];
      [
        'businessTypeRepository',
        'leadServiceRepository',
        'leadChannelRepository',
        'leadformRepository',
        'saleRangeRepository',
        'transactionSizeRepository'
      ].forEach(repoName => {
        const repository = require('../mock/repositories/' + repoName);
        repositories[repoName] = repository;
      });
    
      useCase = createLeadformUseCase({ repositories, errorHandler, logger, leadformService, leadformValidator });
      
    });

    it('Should success', async () => {
      const mockedPayload = {
        "ownerType": "company",
        "taxId": "1098576453613",
        "ownerName": "Tesla Motors",
        "contactName": "Elon Musk",
        "contactEmail": "<EMAIL>",
        "contactMobile": "0869768859",
        "merchantNameEn": "Tesla Motors",
        "merchantNameTh": "เทสล่า มอเตอร์",
        "businessCategoryUuid": "0dc4db49-998d-4702-8add-15935bd97bd2",
        "saleRangeUuid": "ba59d775-bc07-4634-985a-cff55b5c6a10",
        "transactionSizeUuid": "7aa729e8-9936-4c43-8bdc-f8b46a52ac30",
        "proportionForeignPay": 20000000,
        "leadServices": ["4d9a46bc-60ef-4be4-b1d2-763a5cc92860", "e7b2a2f8-2bd9-4fbd-85ba-d14b53b5ccc8"],
        "leadChannels": ["9d6a9fc1-1087-48ee-be31-458e05a1760e"],
        "otherApplicationChannel": "",
        "leadLanguage": "en"
      };

      const data = await useCase.create(null, mockedPayload);

      expect(data).toEqual(expect.objectContaining({
        leadformUuid: expect.any(String),
        created_at: expect.any(String)
      }));
      expect(data.leadformUuid).toHaveLength(36);
    });
  });

  describe('Failure cases', () => {
    it('Should fail on falsy payload', async () => {
      let error;
      try {
        const data = await useCase.create(null, {});
      } catch(err) {
        error = err.message
      }
      expect(error).toEqual('Test Error');
    });

    it('Should fail on referential error', async () => {
      const logger = {
        debug: message => {
          return true;
        },
        error: message => {
          return true;
        }
      }

      const testLeadformService = {
        validateLeadformReferenceData: {
          getLeadformReferenceData: value => ({ data: [], error: 'error' })
        }
      };

      const testRepositories = [];
      [
        'businessTypeRepository',
        'leadServiceRepository',
        'leadChannelRepository',
        'leadformRepository',
        'saleRangeRepository',
        'transactionSizeRepository'
      ].forEach(repoName => {
        const repository = require('../mock/repositories/' + repoName);
        testRepositories[repoName] = repository;
      });

      const mockedLeadformPayload = {
        "ownerType": "individual",
        "taxId": "1098576453613",
        "ownerName": "Tesla Motors",
        "contactName": "Elon Musk",
        "contactEmail": "<EMAIL>",
        "contactMobile": "0869768859",
        "leadServices": ["1234"],
        "leadChannels": ["1234"],
        "otherApplicationChannel": "FUCK",
        "leadLanguage": "en"
      };
      let error;
      const failedUseCase = createLeadformUseCase({ repositories: testRepositories, errorHandler, logger, leadformService: testLeadformService, leadformValidator });
      try {
        const data = await failedUseCase.create(null, mockedLeadformPayload);
      } catch(err) {
        error = err.message;
      }

      expect(error).toEqual('Test Error');
    });
  });
});