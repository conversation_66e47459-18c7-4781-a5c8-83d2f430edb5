const glob = require('glob');
const path = require('path');

module.exports = ({ core }) => {

  const { common: { Server, Router } } = core;

  return {
    run: () => Promise.resolve()
      .then(() => {
        glob.sync(`${path.resolve(__dirname, 'controllers')}/**/index.js`).forEach(routeFile => {
          Router.addController(require(routeFile)());
        });        
        Server.applyRoutes(Router).startServer().logRoutes();
      })
  }
  
};

