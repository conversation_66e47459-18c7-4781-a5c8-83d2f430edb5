const container = require('@src/container');
const { getInitialFormData } = require('@app/formInit');

module.exports = () => {

  const { core: { common }, repositories } = container.cradle;
  const { Controller, Response, Error: errorHandler } = common;

  const initLeadFormUseCase = getInitialFormData({...repositories, errorHandler});

  const controller = new Controller();
  controller.method = 'GET';
  controller.path = '/v1/portal/servicerequests/forminit';
  controller.handler = async (req, res, next) => {
    const response = new Response().bindContext(res, next);
    try {
      const data = await initLeadFormUseCase.initForm();
      return response.success('success', data);
    } catch (error) {
      return response.error(error);
    }
  };

  return controller;
}