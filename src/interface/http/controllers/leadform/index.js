const container = require('@src/container');
const leadform = require('@domain/leadform');
const { createLeadform } = require('@app/leadform');

module.exports = () => {

  const {
    core: { common },
    repositories, validators: { leadformValidator },
    services: { leadform: leadformService },
  } = container.cradle;

  const { Controller, Response, Error: errorHandler, Log: logger } = common;

  const createLeadformUsecase = createLeadform({ repositories, leadformValidator, errorHandler, leadformService, logger });
  
  const controller = new Controller();
  controller.method = 'POST';
  controller.path = '/v1/portal/servicerequests/leadform';
  controller.handler = async (req, res, next) => {
    const response = new Response().bindContext(res, next);
    try {
      if (!req.headers['useruuid']) {
        throw errorHandler.lookupType('missing_param', { message: 'No user unique identifier parsed with the request' });
      }
      const createLeadformDTO = await createLeadformUsecase.create(req.headers.useruuid, { ...req.body });
      return response.success('success', createLeadformDTO);
    } catch (error) {
      return response.error(error);
    }
  };

  return controller;
}