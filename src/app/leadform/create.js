const leadform = require('@domain/leadform');

module.exports = ({
  repositories,
  leadformValidator,
  errorHandler,
  leadformService,
  logger
}) => {
  const create = async (userUuid, leadformDTO) => {
    try {
      const result = leadformValidator().validate(leadformDTO);

      const { leadformRepository } = repositories;

      if(result.error) {
        throw { type: 'invalid_param', details: { message: result.error.details[0].message } };
      }

      const leadformReferenceData = await leadformService.validateLeadformReferenceData.getLeadformReferenceData(result.value);

      if(leadformReferenceData.error) {
        throw { type: 'invalid_param', details: { message: leadformReferenceData.error.message } };
      }

      logger.debug('Creating the LeadForm entity ...');

      const entity = leadform(result.value);
      logger.debug(`Successfully instantiated the LeadForm entity -> ${JSON.stringify(entity)}`);
      
      const leadformEntity = await leadformRepository.create(entity);
      logger.debug(`LeadForm entity successfully persisted -> ${JSON.stringify(leadformEntity)}`);

      logger.debug('Transforming LeadForm entity into DTO for processing the notification ...');

      const notificationDTO = {
        ...leadformEntity,
        userUuid,
        lang: result.value.leadLanguage,
        leadServices: leadformReferenceData.data.leadServices,
        leadChannels: leadformReferenceData.data.leadChannels,
        businessCategory: leadformReferenceData.data.businessCategory,
        businessType: leadformReferenceData.data.businessType,
        saleRange: leadformReferenceData.data.saleRange,
        transactionSize: leadformReferenceData.data.transactionSize
      }

      logger.debug('[NOTIFICATION PAYLOAD]:' + JSON.stringify(notificationDTO));
      logger.debug('Start processing notifications ...');

      await leadformService.notifyLeadformCreation.notify(notificationDTO);

      logger.debug('Notification sent successfully');
      logger.debug('Preparing the DTO for the response ...');

      return {
        leadformUuid: leadformEntity.leadFormUuid,
        created_at: leadformEntity.created_at.toString()
      };

    } catch (error) {
      logger.error(error);
      throw errorHandler.lookupType(error.type, { ...error.details });
    }
  }

  return {
    create
  }
}