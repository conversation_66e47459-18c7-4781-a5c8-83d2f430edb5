module.exports = ({
  businessTypeRepository,
  leadServiceRepository,
  leadChannelRepository,
  saleRangeRepository,
  transactionSizeRepository,
  errorHandler
}) => {
  const initForm = () => {
    return Promise.resolve()
      .then(() => {
        const resolvers = [
          businessTypeRepository.retrieveAll(),
          leadServiceRepository.retrieveAll(),
          leadChannelRepository.retrieveAll(),
          saleRangeRepository.retrieveAll(),
          transactionSizeRepository.retrieveAll()
        ];
        return Promise.all(resolvers);
      }).then(([businessTypes, leadServices, leadChannels, saleRanges, transactionSizes]) => {
        return {
          businessTypes,
          leadServices,
          leadChannels,
          saleRanges,
          transactionSizes
        };
      })
      .catch(error => {
        throw errorHandler.lookupType(error.type, { ...error.details });
      });
  }

  return {
    initForm
  }
};