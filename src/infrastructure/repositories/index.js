const businessType = require('./businessType');
const businessCategory = require('./businessCategory');
const leadService = require('./leadService');
const leadChannel = require('./leadChannel');
const saleRange = require('./saleRange');
const transactionSize = require('./transactionSize');
const leadform = require('./leadform');

module.exports = ({ database, objectStorage }) => {

  const businessTypeModel = database.models.businessType;
  const businessCategoryModel = database.models.businessCategory;
  const leadServiceModel = database.models.leadService;
  const leadChannelModel = database.models.leadChannel;
  const saleRangeModel = database.models.saleRange;
  const transactionSizeModel = database.models.transactionSize;
  const leadformModel = database.models.leadform;

  return {
    businessTypeRepository: businessType({ businessTypeModel, businessCategoryModel }),
    businessCategoryRepository: businessCategory({ businessCategoryModel }),
    leadServiceRepository: leadService({ leadServiceModel, objectStorage }),
    leadChannelRepository: leadChannel({ leadChannelModel }),
    saleRangeRepository: saleRange({ saleRangeModel }),
    transactionSizeRepository: transactionSize({ transactionSizeModel }),
    leadformRepository: leadform({ leadformModel, leadServiceModel, leadChannelModel })
  }
}