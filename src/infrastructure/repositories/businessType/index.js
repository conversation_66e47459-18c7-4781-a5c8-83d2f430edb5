const toEntity = require('@domain/businessType');

module.exports = ({ businessTypeModel, businessCategoryModel }) => {

  const retrieveAll = () => 
    businessTypeModel.findAll({ include: businessCategoryModel }).then(dbResult =>
      dbResult.map(({ dataValues }) =>
        toEntity(dataValues)
      )
    ).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of business types' } };
    });

  const retrieveOneByUuid = uuid => {
    return businessTypeModel.findByPk(uuid, { include: businessCategoryModel })
    .then(result => {
      return toEntity(result.dataValues);
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to find a matched business type' } };
    });
  }

  return {
    retrieveAll,
    retrieveOneByUuid
  }
};