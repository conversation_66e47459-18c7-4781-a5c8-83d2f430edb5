const toEntity = require('@domain/saleRange');

module.exports = ({ saleRangeModel }) => {

  const retrieveAll = () => 
    saleRangeModel.findAll().then(dbResult =>
      dbResult.map(({ dataValues }) =>
        toEntity(dataValues)
      )
    ).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of sale ranges' } };
    });
  
  const retrieveOneByUuid = uuid => {
    return saleRangeModel.findByPk(uuid)
    .then(result => {
      return toEntity(result.dataValues);
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the requested sale range' } };
    });
  }

  return {
    retrieveAll,
    retrieveOneByUuid
  }
};