const toEntity = require('@domain/leadService');

module.exports = ({ leadServiceModel, objectStorage }) => {

  const retrieveAll = () => 
    leadServiceModel.findAll().then(dbResult => {
      return Promise.all(dbResult.map(({ dataValues }) => {
        return objectStorage.generatePresignedURL(dataValues.s3_bucketName, dataValues.s3_bucketKey)
          .then(url => {
            dataValues.url = url;
            return toEntity(dataValues);
          });
      }));
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of lead services' } };
    });

    const retrieveOneByUuid = uuid => {
      return leadServiceModel.findByPk(uuid)
      .then(result => {
        return toEntity(result.dataValues);
      }).catch(error => {
        throw { type: 'database_error', details: { message: 'Unable to find the requested lead service' } };
      });
    }

    const retrieveFromList = uuids => {
      return leadServiceModel.findAll({
        where: {
          leadServiceUuid: uuids
        }
      }).then(resp => {
        return resp.map(service => {
          return toEntity(service.dataValues);
        });
      }).catch(error => {
        throw { type: 'database_error', details: { message: 'Unable to fetch the list of lead services' } };
      });
    }

  return {
    retrieveAll,
    retrieveOneByUuid,
    retrieveFromList
  }
};