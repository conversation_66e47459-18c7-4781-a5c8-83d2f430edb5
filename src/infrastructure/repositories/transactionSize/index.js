const toEntity = require('@domain/transactionSize');

module.exports = ({ transactionSizeModel }) => {

  const retrieveAll = () => 
    transactionSizeModel.findAll().then(dbResult =>
      dbResult.map(({ dataValues }) =>
        toEntity(dataValues)
      )
    ).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of transaction sizes' } };
    });

  const retrieveOneByUuid = uuid => {
    return transactionSizeModel.findByPk(uuid)
    .then(result => {
      return toEntity(result.dataValues);
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to find the requested transaction size' } };
    });
  }

  return {
    retrieveAll,
    retrieveOneByUuid
  }
};