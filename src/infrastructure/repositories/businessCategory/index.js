const toEntity = require('@domain/businessCategory');

module.exports = ({ businessCategoryModel }) => {

  const retrieveOneByUuid = uuid => {
    return businessCategoryModel.findByPk(uuid)
    .then(result => {
      return toEntity(result.dataValues);
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to find a matched business category' } };
    });
  }

  return {
    retrieveOneByUuid
  }
};