const toEntity = require('@domain/leadform');

module.exports = ({ leadformModel }) => {
  const create = async leadform => {
    try {
      const leadformInstance = leadformModel.build({ ...leadform });
      leadformInstance.addLeadServices(leadform.leadServices);
      leadformInstance.addLeadChannels(leadform.leadChannels);
      const savedLeadform = await leadformInstance.save();

      const leadServices = await savedLeadform.getLeadServices().map(leadService => leadService.leadServiceUuid);
      const leadChannels = await savedLeadform.getLeadChannels().map(leadChannel => leadChannel.leadChannelUuid);

      savedLeadform.dataValues.leadServices = leadServices;
      savedLeadform.dataValues.leadChannels = leadChannels;

      const { dataValues } = savedLeadform;

      return toEntity(dataValues);
    } catch (error) {
      throw { type: 'database_error', details: { message: 'Unable to persist the lead form' } };
    }
  }

  return { create };
}