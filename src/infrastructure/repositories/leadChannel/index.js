const toEntity = require('@domain/leadChannel');

module.exports = ({ leadChannelModel }) => {

  const retrieveAll = () => 
    leadChannelModel.findAll().then(dbResult =>
      dbResult.map(({ dataValues }) =>
        toEntity(dataValues)
      )
    ).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of lead channels' } };
    });

  const retrieveOneByUuid = uuid => {
    return leadChannelModel.findByPk(uuid)
    .then(result => {
      return toEntity(result.dataValues);
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the requested lead channel' } };
    });
  }

  const retrieveFromList = uuids => {
    return leadChannelModel.findAll({
      where: {
        leadChannelUuid: uuids
      }
    }).then(result => {
      return result.map(({ dataValues }) => {
        return toEntity(dataValues);
      });
    }).catch(error => {
      throw { type: 'database_error', details: { message: 'Unable to fetch the list of lead channels from the list' } };
    });
  }

  return {
    retrieveAll,
    retrieveOneByUuid,
    retrieveFromList
  }
};