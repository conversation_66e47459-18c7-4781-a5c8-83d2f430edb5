const AWS = require('aws-sdk');

module.exports = ({ config }) => {

  const S3 = new AWS.S3({
    region: config.aws.region,
    signatureVersion: config.aws.signatureVersion
  });

  const generatePresignedURL = (bucketName, bucketKey, signedURLValidityPeriod = 60 * 5) => {
    return new Promise((resolve, reject) => {
      S3.getSignedUrl('getObject', {
        Bucket: bucketName,
        Key:  bucketKey,
        Expires: signedURLValidityPeriod
      }, (err, url) => {
        if (err) reject(err);
        resolve(url);
      });
    });
  }

  return {
    generatePresignedURL
  }
};