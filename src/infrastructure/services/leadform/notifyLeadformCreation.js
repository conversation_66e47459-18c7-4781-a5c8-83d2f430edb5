const moment = require('moment');

module.exports = ({ config, db, logger, mailer }) => {
  const notify = async (leadformNotificationDTO) => {
    try {
      const [[userInfo]] = await db.sequelize.query(`SELECT * FROM userinfo WHERE portalUuid = '${leadformNotificationDTO.userUuid}'`);
      logger.debug(`[USER INFO] ${JSON.stringify(userInfo)}`);
      
      if (!userInfo) {
        throw { type: 'not_found', message: 'Unable to find the requested user' };
      }
      
      const externalEmailTemplateName = `leadformrequester_${leadformNotificationDTO.lang}`;
      const internalEmailTemplateName = leadformNotificationDTO.ownerType === 'company' ? 'leadforminternal' : 'leadforminternalnomerchant';

      const capitalisedLang = leadformNotificationDTO.lang.charAt(0).toUpperCase() + leadformNotificationDTO.lang.slice(1);

      const apiServices = JSON.stringify(leadformNotificationDTO.leadServices.map(service => service['name' + capitalisedLang]));
      const applicationChannels = JSON.stringify(leadformNotificationDTO.leadChannels.map(channel => channel['name' + capitalisedLang]));

      const genericEmailPayload = {
        requestNo: leadformNotificationDTO.requestNo,
        userFirstname: userInfo.firstName,
        userLastname: userInfo.lastName,
        createdDatetime: moment.unix(leadformNotificationDTO.created_at).local().format('DD MMM YYYY - HH:mm'),
        apiServices
      }

      const genericInternalEmailPayload = {
        recipient: JSON.stringify(config.email.internalRecipient),
        recipientCc: JSON.stringify(config.email.internalRecipientCC),
        ownerType: leadformNotificationDTO.ownerType,
        ownerName: leadformNotificationDTO.ownerName,
        taxId: leadformNotificationDTO.taxId,
        contactName: leadformNotificationDTO.contactName,
        contactEmail: leadformNotificationDTO.contactEmail,
        contactMobile: leadformNotificationDTO.contactMobile,
        applicationChannels,
        otherApplicationChannel: leadformNotificationDTO.otherApplicationChannel
      }

      const internalPayload = leadformNotificationDTO.ownerType === 'company' ?
        {
          channel: 'leadform',
          emailTemplateType: internalEmailTemplateName,
          emailPayload: {
            ...genericEmailPayload,
            ...genericInternalEmailPayload,
            merchantNameEn: leadformNotificationDTO.merchantNameEn,
            merchantNameTh: leadformNotificationDTO.merchantNameTh,
            businessType: leadformNotificationDTO.businessType.nameEn,
            businessCategory: leadformNotificationDTO.businessCategory.nameEn,
            saleRange: leadformNotificationDTO.saleRange.value,
            transactionSize: leadformNotificationDTO.transactionSize.value,
            proportionForeignPay: parseInt(leadformNotificationDTO.proportionForeignPay)
          }
        } :
        {
          channel: 'leadform',
          emailTemplateType: internalEmailTemplateName,
          emailPayload: {
            ...genericEmailPayload,
            ...genericInternalEmailPayload
          }
        };

      const genericExternalEmailPayload = {
        recipient: JSON.stringify([userInfo.email])
      }

      const externalPayload = {
        channel: 'leadform',
        emailTemplateType: externalEmailTemplateName,
        emailPayload: {
          ...genericEmailPayload,
          ...genericExternalEmailPayload
        }
      }

      if (config.email.deliver) {
        await mailer.send(internalPayload, { language: leadformNotificationDTO.lang });
        await mailer.send(externalPayload, { language: leadformNotificationDTO.lang });
      }

    } catch (error) {
      logger.error(error);
      throw { type: error.type, details: { message: 'Unable to process the notification' } };
    }
  }

  return {
    notify
  }
};