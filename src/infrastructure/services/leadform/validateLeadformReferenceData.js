module.exports = ({
  businessCategoryRepository,
  businessTypeRepository,
  saleRangeRepository,
  leadServiceRepository,
  leadChannelRepository,
  transactionSizeRepository
}) => {
  const getLeadformReferenceData = async leadform => {
    try {

      const leadServices = await leadServiceRepository.retrieveFromList(leadform.leadServices);
      const leadChannels = await leadChannelRepository.retrieveFromList(leadform.leadChannels);
      let businessCategory,
          businessType,
          saleRange,
          transactionSize;

      if (leadform.ownerType === 'company') {
        businessCategory = await businessCategoryRepository.retrieveOneByUuid(leadform.businessCategoryUuid);
        businessType = await businessTypeRepository.retrieveOneByUuid(businessCategory.businessTypeUuid);
        saleRange = await saleRangeRepository.retrieveOneByUuid(leadform.saleRangeUuid);
        transactionSize = await transactionSizeRepository.retrieveOneByUuid(leadform.transactionSizeUuid);
      }

      if (!validateLeadValue(leadServices, leadform.leadServices, 'leadServices')) {
        throw new Error('Unmatched lead services between client and server');
      }

      if (!validateLeadValue(leadChannels, leadform.leadChannels, 'leadChannels')) {
        throw new Error('Unmatched lead channels between client and server');
      }

      return { 
        error: null,
        data: leadform.ownerType === 'individual' ?
          { leadChannels, leadServices } :
          { businessCategory, businessType, saleRange, transactionSize, leadServices, leadChannels }
      };

    } catch (error) {
      return {
        error,
        data: null
      }
    }
  }

  return { getLeadformReferenceData };
}

const validateLeadValue = (leadValueDB, leadValue, param) => {
  const mapField = {'leadServices': 'leadServiceUuid', 'leadChannels': 'leadChannelUuid'};
  leadValueDB = leadValueDB.map(channel => channel[mapField[param]]);
  let notExists = [];

  leadValue.map(channel => {
    if (!leadValueDB.includes(channel)){
      notExists.push(channel);
    }
  })

  if (notExists.length > 0){
    return false;
  }
   return true;
}