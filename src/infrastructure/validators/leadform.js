const Joi = require('joi');

module.exports = () => {
  const validate = (createLeadFormDTO) => {
      const schema = Joi.object({
      ownerType: Joi.string().valid(['company', 'individual']).required(),
      taxId: Joi.string().min(13).max(13).required(),
      ownerName: Joi.string().max(50).required(),
      contactName: Joi.string().max(50).required(),
      contactEmail: Joi.string().email().max(50).required(),
      contactMobile: Joi.string().min(10).max(10).required(),
      merchantNameEn: Joi.when('ownerType', {
        is: 'company',
        then: Joi.string().max(50).required(),
        otherwise: Joi.forbidden()
      }),
      merchantNameTh: Joi.when('ownerType', {
        is: 'company',
        then: Joi.string().max(50).required(),
        otherwise: Joi.forbidden()
      }),
      businessCategoryUuid: Joi.when('ownerType', {
        is: 'company',
        then: Joi.string().max(36).required(),
        otherwise: Joi.forbidden()
      }),
      saleRangeUuid: Joi.when('ownerType', {
        is: 'company',
        then: Joi.string().max(36).required(),
        otherwise: Joi.forbidden()
      }),
      transactionSizeUuid: Joi.when('ownerType', {
        is: 'company',
        then: Joi.string().max(36).required(),
        otherwise: Joi.forbidden()
      }),
      proportionForeignPay: Joi.number().max(9999999999).optional(),
      leadServices: Joi.array().required(),
      leadChannels: Joi.array().items(Joi.string()).min(1).unique(),
      otherApplicationChannel: Joi.when('leadChannels', {
        is: Joi.array().items(Joi.string().valid(['others'])),
        then: Joi.string().max(50).required(),
        otherwise: Joi.optional()
      }),
      leadLanguage: Joi.string().valid(['en', 'th']).required()
    });
    return schema.validate(createLeadFormDTO);
  }
  
  return {
    validate
  }
  
};