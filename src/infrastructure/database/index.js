const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');

module.exports = ({ config }) => {
  const sequelize = new Sequelize(
    config.rds.database,
    process.env.DB_USER_PORTAL,
    process.env.DB_PASSWORD_PORTAL,
    {
      host: config.rds.host,
      port: config.rds.port,
      dialect: 'mysql',
      logging: false
    }
  );

  const models = {};

  const modelDirectory = path.resolve(__dirname, './models');

  fs.readdirSync(modelDirectory).forEach(modelFile => {
    const modelFilePath = path.resolve(modelDirectory, modelFile);
    const model = sequelize.import(modelFilePath);
    models[model.name] = model;
  });

  Object.keys(models).forEach(k => {
    if (models[k].associate) {
      models[k].associate(models);
    }
  });

  return {
    sequelize,
    models
  }
};