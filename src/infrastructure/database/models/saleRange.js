module.exports = (sequelize, DataTypes) => {

  const SaleRange = sequelize.define('saleRange', {
    saleRangeUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    value: {
      type: DataTypes.STRING(60)
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'sale_range',
    timestamps: false
  });

  return SaleRange;
};