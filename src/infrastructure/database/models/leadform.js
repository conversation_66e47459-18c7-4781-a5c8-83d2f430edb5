const generateRequestNo = require('@infrastructure/helpers/generateRequestNo');

module.exports = (sequelize, DataTypes) => {
  const LeadForm = sequelize.define('leadform', {
    leadFormUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    requestNo: {
      type: DataTypes.STRING(14),
      allowNull: false
    },
    ownerType: {
      type: DataTypes.ENUM(['company', 'individual']),
      allowNull: false
    },
    taxId: {
      type: DataTypes.STRING(13),
      allowNull: false
    },
    ownerName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    contactName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    contactEmail: {
      type: DataTypes.STRING,
      allowNull: false
    },
    contactMobile: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    merchantNameEn: {
      type: DataTypes.STRING,
      allowNull: true
    },
    merchantNameTh: {
      type: DataTypes.STRING,
      allowNull: true
    },
    businessCategoryUuid: {
      type: DataTypes.UUID,
      allowNull: true
    },
    saleRangeUuid: {
      type: DataTypes.UUID,
      allowNull: true
    },
    transactionSizeUuid: {
      type: DataTypes.UUID,
      allowNull: true
    },
    proportionForeignPay: {
      type: DataTypes.DECIMAL(10, 0),
      allowNull: true
    },
    otherApplicationChannel: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'leadform',
    timestamps: false,
    hooks: {
      beforeValidate(record) {
        record.dataValues.created_at = Math.floor(Date.now() / 1000);
        record.dataValues.updated_at = Math.floor(Date.now() / 1000);
        record.dataValues.requestNo = generateRequestNo();
      }
    }
  });

  LeadForm.associate = models => {
    LeadForm.belongsToMany(models.leadChannel, {
      through: 'leadFormChannel',
      as: 'leadChannels',
      foreignKey: 'leadFormUuid',
      otherKey: 'leadChannelUuid'
    });

    LeadForm.belongsToMany(models.leadService, {
      through: 'leadFormService',
      as: 'leadServices',
      foreignKey: 'leadFormUuid',
      otherKey: 'leadServiceUuid'
    });
  }



  return LeadForm;
}