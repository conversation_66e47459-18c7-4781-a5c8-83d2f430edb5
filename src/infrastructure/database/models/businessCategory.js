const BusinessType = require('./businessType');

module.exports = (sequelize, DataTypes) => {
  const BusinessCategory = sequelize.define('businessCategory', {
    businessCategoryUuid: {
      type: DataTypes.UUID,
      defaultVaue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    nameTh: {
      type: DataTypes.STRING(255)
    },
    nameEn: {
      type: {
        type: DataTypes.STRING(255)
      }
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'business_category',
    timestamps: false
  });

  BusinessCategory.associate = models => {
    BusinessCategory.belongsTo(models.businessType, { foreignKey: 'businessTypeUuid' })
  };

  return BusinessCategory;
};