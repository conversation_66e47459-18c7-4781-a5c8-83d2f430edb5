module.exports = (sequelize, DataTypes) => {
  const LeadChannel = sequelize.define('leadChannel', {
    leadChannelUuid: {
      type: DataTypes.UUID,
      defaultVaue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    nameTh: {
      type: DataTypes.STRING(60)
    },
    nameEn: {
      type: {
        type: DataTypes.STRING(60)
      }
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    sequence_id: {
      type: DataTypes.INTEGER(11),
      allowNull: true
    }
  }, {
    tableName: 'lead_channel',
    timestamps: false
  });

  LeadChannel.addScope('defaultScope', {
    order: [['sequence_id', 'ASC']]
  }, { ovverride: true });

  LeadChannel.associate = models => {
    LeadChannel.belongsToMany(models.leadform, {
      through: 'leadFormChannel',
      as: 'leadforms',
      foreignKey: 'leadChannelUuid',
      otherKey: 'leadFormUuid'
    });
  }

  return LeadChannel;
};