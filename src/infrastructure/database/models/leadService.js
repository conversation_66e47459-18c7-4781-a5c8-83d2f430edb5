module.exports = (sequelize, DataTypes) => {

  const LeadService = sequelize.define('leadService', {
    leadServiceUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    nameEn: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    nameTh: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    s3_bucketName: {
      type: DataTypes.STRING(128),
      allowNull: true
    },
    s3_bucketKey: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'lead_service',
    timestamps: false
  });

  LeadService.associate = models => {
    LeadService.belongsToMany(models.leadform, {
      through: 'leadFormService',
      as: 'leadforms',
      foreignKey: 'leadServiceUuid',
      otherKey: 'leadFormUuid'
    });
  }

  return LeadService;
};