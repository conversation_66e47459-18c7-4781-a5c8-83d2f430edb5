const BusinessCategory = require('./businessCategory');

module.exports = (sequelize, DataTypes) => {
  const BusinessType = sequelize.define('businessType', {
    businessTypeUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    nameTh: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    nameEn: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    sequence_id: {
      type: DataTypes.INTEGER(11),
      allowNull: true
    }
  }, {
    tableName: 'business_type',
    timestamps: false
  });

  BusinessType.addScope('defaultScope', {
    order: [['sequence_id', 'ASC']]
  }, { override: true });

  BusinessType.associate = models => {
    BusinessType.hasMany(models.businessCategory, {
      foreignKey: 'businessTypeUuid'
    });
  };

  return BusinessType;
};