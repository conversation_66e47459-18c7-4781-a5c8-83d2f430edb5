module.exports = (sequelize, DataTypes) => {
  const TransactionSize = sequelize.define('transactionSize', {
    transactionSizeUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    value: {
      type: DataTypes.STRING(60)
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'transaction_size',
    timestamps: false
  });

  return TransactionSize;
};