module.exports = (sequelize, DataTypes) => {
  const LeadFormChannel = sequelize.define('leadFormChannel', {
    leadFormChannelUuid: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    leadFormUuid: {
      type: DataTypes.UUID,
      allowNull: false
    },
    leadChannelUuid: {
      type: DataTypes.UUID,
      allowNull: false
    },
    created_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    },
    updated_at: {
      type: DataTypes.BIGINT(20),
      allowNull: false
    }
  }, {
    tableName: 'lead_form_channel',
    timestamps: false,
    hooks: {
      beforeBulkCreate(records) {
        records.forEach(record => {
          record.dataValues.created_at = Math.floor(Date.now() / 1000);
          record.dataValues.updated_at = Math.floor(Date.now() / 1000);
        });
      }
    }
  });

  return LeadFormChannel;
};