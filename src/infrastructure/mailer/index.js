const axios = require('axios');
const uuid = require('uuid/v4');

module.exports = ({ config }) => {

  const { microServiceApiBasePath } = config;

  const send = (payload, options = {}) => {
    try {
      return axios.post(`${microServiceApiBasePath}v1/portal/email`, payload, {
      // return axios.post(`http://localhost:5050/v1/portal/email`, payload, {
        headers: {
          'accept-language': options.language || 'en',
          host: microServiceApiBasePath.replace(/^http[s]*:\/\/|\/.*$/g, ''),
          requestUId: uuid(),
          resourceOwnerId: uuid()
        }
      });
    } catch (error) {
      throw { type: 'invalid_downstream_response', details: { message: 'Unable to send the email' } };
    }
  }

  return { send };
}