const { createContainer, asFunction } = require('awilix');

// Dependencies
const App = require('@app');
const Server = require('@interface/http/server');
const Config = require('@src/config');
const Core = require('@src/core');
const Database = require('@infrastructure/database');
const Repositories = require('@infrastructure/repositories');
const ObjectStorage = require('@infrastructure/objectStorage');
const Validators = require('@infrastructure/validators');
const Helpers = require('@infrastructure/helpers');
const Mailer = require('@infrastructure/mailer');
const Services = require('@infrastructure/services');

const container = createContainer();

container.register({
  app: asFunction(App).singleton(),
  server: asFunction(Server).singleton(),
  config: asFunction(Config).singleton(),
  core: asFunction(Core).singleton(),
  database: asFunction(Database).singleton(),
  repositories: asFunction(Repositories).singleton(),
  objectStorage: asFunction(ObjectStorage).singleton(),
  validators: asFunction(Validators).singleton(),
  helpers: asFunction(Helpers).singleton(),
  services: asFunction(Services).singleton(),
  mailer: asFunction(Mailer).singleton()
});

module.exports = container;