{"rds": {"host": "rdsportalmsbeerpci.cloud.scb", "database": "customportal", "port": "3308"}, "email": {"deliver": true, "internalRecipient": "<EMAIL>", "internalRecipientCC": ["<EMAIL>", "<EMAIL>"]}, "aws": {"bucket_name": "partner-eco-pci", "bucket_folder": "onboarding/test-results", "user_credentials_bucket_folder": "onboarding/user-credentials", "signatureVersion": "v4"}, "microServiceApiBasePath": "https://mslbeco-eks-prod.scb.co.th:443/", "microServiceCertPath": "", "apiGwBasePath": "https://api-prod.scb.co.th/", "portalApiBasePath": "https://apim-ssg.scb.co.th", "applicationBasePath": "http://prod-scb-partner-v1-portal-applications.pe-prod.svc.cluster.local/", "portalApiPort": 9443, "portalApiHostname": "apim-ssg.scb.co.th", "portalTenantId": "developer-prod"}