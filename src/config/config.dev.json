{"rds": {"host": "rdsportalmsbeerpcid.devcloud.scb", "database": "customportal_dev", "port": "3307"}, "email": {"deliver": true, "internalRecipient": "chatthana.j<PERSON><PERSON><PERSON><PERSON>@scb.co.th", "internalRecipientCC": ["<EMAIL>"]}, "aws": {"bucket_name": "partnereco-pci-dev", "bucket_folder": "onboarding/test-results", "user_credentials_bucket_folder": "onboarding/user-credentials", "signatureVersion": "v4"}, "microServiceApiBasePath": "https://mslbeco-eks-dev.se.scb.co.th:443/", "microServiceCertPath": "mslbecou.pem", "portalApiBasePath": "https://apimdevpci-ssg.se.scb.co.th", "apiGwBasePath": "https://api-dev.se.scb.co.th/", "applicationBasePath": "http://dev-scb-partner-v1-portal-applications.pe-dev.svc.cluster.local/", "portalApiPort": 9443, "portalApiHostname": "apimdevpci-ssg.se.scb.co.th", "portalTenantId": "developer-dev-pci", "logLevel": "debug"}