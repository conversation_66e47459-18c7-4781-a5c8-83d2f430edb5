{"rds": {"host": "rdsportalmsbeerpcis.devcloud.scb", "database": "customportal_sit", "port": "3307"}, "email": {"deliver": true, "internalRecipient": "<EMAIL>", "internalRecipientCC": ["<EMAIL>"]}, "aws": {"bucket_name": "partner-eco-pci-sit", "bucket_folder": "onboarding/test-results", "user_credentials_bucket_folder": "onboarding/user-credentials", "signatureVersion": "v4"}, "microServiceApiBasePath": "https://mslbeco-eks-sit.se.scb.co.th:443/", "microServiceCertPath": "", "portalApiBasePath": "https://apim-ssg.se.scb.co.th", "apiGwBasePath": "https://api-sit.se.scb.co.th/", "applicationBasePath": "http://sit-scb-partner-v1-portal-applications.pe-sit.svc.cluster.local/", "portalApiPort": 9443, "portalApiHostname": "apim-ssg.se.scb.co.th", "portalTenantId": "developer-sit", "logLevel": "debug"}