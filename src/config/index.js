const deepmerge = require('deepmerge');
const { getConfigObject } = require('microservice-config/envparser');
const microserviceConfig = require('microservice-config');

const globals = {
  apiServiceName: 'servicerequests-v1',
  apiBasePath: 'portal/servicerequests',
  apiVersion: 'v1',
  msVersion: require('@root/package.json').version,

  // any ms global config here...
  determineConfigCrossEnv: () => {
    let currentEnv = process.env.NODE_ENV;

    if(currentEnv === 'local') {
      currentEnv = require(`./config.dev.json`);
    } else if(currentEnv === 'dev') {
      currentEnv = require(`./config.dev.json`);
    } else if(currentEnv === 'sit') {
      currentEnv = require(`./config.dev.json`);
    } else if(currentEnv === 'uat') {
      currentEnv = require(`./config.sit.json`);
    } else if(currentEnv === 'prod') {
      currentEnv = require(`./config.uat.json`);
    } else {
      currentEnv = require(`./config.dev.json`);
    }
    return currentEnv;
  },
};

const configFromFile = require(`./config.${process.env.NODE_ENV || 'local'}.json`);

// merges microserviceConfig with globals and configFromFile giving the later ones a higher priority
const staticConfig = deepmerge(microserviceConfig, deepmerge(globals, configFromFile));

// reads the deployment yml file for env variables and convert them into a config object
const deploymentConfig = getConfigObject();

// merges deploymentConfig with staticConfig giving deploymentConfig a higher priority
const config = deepmerge(staticConfig, deploymentConfig);

module.exports = () => config;
