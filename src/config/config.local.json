{"rds": {"host": "127.0.0.1", "database": "customportal_dev", "port": "13306"}, "email": {"deliver": true, "internalRecipient": "<EMAIL>", "internalRecipientCC": ["<EMAIL>"]}, "aws": {"bucket_name": "partner-eco-pci-dev", "bucket_folder": "onboarding/test-results", "user_credentials_bucket_folder": "onboarding/user-credentials", "signatureVersion": "v4"}, "microServiceApiBasePath": "http://localhost:5050/", "portalApiBasePath": "https://apimdevpci-ssg.se.scb.co.th", "apiGwBasePath": "https://api-pci-dev.se.scb.co.th/", "applicationBasePath": "http://dev-scb-partner-v1-portal-applications.pe-dev.svc.cluster.local/", "portalApiPort": 9443, "portalApiCertPath": "", "portalApiHostname": "", "portalTenantId": "developer-dev-pci", "logLevel": "debug", "local": true}