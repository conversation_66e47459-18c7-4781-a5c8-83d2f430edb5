{"rds": {"host": "rdsportalmsbeerpciu.devcloud.scb", "database": "customportal_uat", "port": "3308"}, "email": {"deliver": true, "internalRecipient": "<EMAIL>", "internalRecipientCC": ["<EMAIL>"]}, "aws": {"bucket_name": "partner-eco-pci-uat", "bucket_folder": "onboarding/test-results", "user_credentials_bucket_folder": "onboarding/user-credentials", "signatureVersion": "v4"}, "microServiceApiBasePath": "https://mslbeco-eks-uat.se.scb.co.th:443/", "microServiceCertPath": "", "portalApiBasePath": "https://apimuat-ssg.se.scb.co.th", "apiGwBasePath": "https://api-uat.se.scb.co.th/", "applicationBasePath": "http://uat-scb-partner-v1-portal-applications.pe-uat.svc.cluster.local/", "portalApiPort": 9443, "portalApiHostname": "apimuat-ssg.se.scb.co.th", "portalTenantId": "developer-uat", "logLevel": "debug"}