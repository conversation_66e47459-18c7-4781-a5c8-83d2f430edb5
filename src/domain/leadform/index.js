const t = require('tcomb');
const { compose } = require('ramda');
const { clean } = require('../helpers');

const Leadform = t.struct({
  leadFormUuid: t.maybe(t.String),
  requestNo: t.maybe(t.String),
  ownerType: t.enums.of(['individual', 'company'], 'ownerType'),
  taxId: t.String,
  ownerName: t.String,
  contactName: t.String,
  contactEmail: t.String,
  contactMobile: t.String,
  merchantNameEn: t.maybe(t.String),
  merchantNameTh: t.maybe(t.String),
  businessCategoryUuid: t.maybe(t.String),
  saleRangeUuid: t.maybe(t.String),
  transactionSizeUuid: t.maybe(t.String),
  proportionForeignPay: t.maybe(t.Number),
  leadServices: t.list(t.String),
  leadChannels: t.list(t.String),
  otherApplicationChannel: t.maybe(t.String),
  created_at: t.maybe(t.Number)
});

module.exports = compose(clean, Leadform);