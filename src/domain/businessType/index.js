const t = require('tcomb');
const { compose } = require('ramda');
const { clean } = require('../helpers');

const businessCategory = t.struct({
  businessCategoryUuid: t.String,
  nameTh: t.String,
  nameEn: t.String
}, 'businessCategory')

const BusinessType =  t.struct({
  businessTypeUuid: t.String,
  nameTh: t.String,
  nameEn: t.String,
  businessCategories: t.list(businessCategory)
}, 'BusinessType');

module.exports = compose(clean, BusinessType);
